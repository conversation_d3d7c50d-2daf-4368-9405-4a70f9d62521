"use client";

import { useBusiness } from "@/context/business-context";
import { getBranchStatus, isBranchOpen } from "@/services/business-api";
import { useCart } from "@/context/cart-context";

type BusinessHoursStatusProps = {
  className?: string;
  showDetails?: boolean;
};

export default function BusinessHoursStatus({ 
  className = "", 
  showDetails = false 
}: BusinessHoursStatusProps) {
  const { selectedBranch } = useBusiness();
  const { orderType } = useCart();

  if (!selectedBranch) {
    return null;
  }

  // Determine service type based on order type
  const serviceType = orderType === "pickup" ? "pickup" : "delivery";
  
  // Get current status
  const isOpen = isBranchOpen(selectedBranch, serviceType);
  const status = getBranchStatus(selectedBranch, serviceType);

  // Status styling
  const statusClasses = isOpen 
    ? "text-green-600 bg-green-50 border-green-200" 
    : "text-red-600 bg-red-50 border-red-200";

  const statusText = status.status === "open_24_7" 
    ? "Open 24/7" 
    : isOpen 
      ? "Open Now" 
      : "Closed";

  return (
    <div className={`inline-flex items-center gap-2 ${className}`}>
      {/* Status indicator */}
      <div className={`px-2 py-1 rounded-full text-xs font-medium border ${statusClasses}`}>
        <div className="flex items-center gap-1">
          <div className={`w-2 h-2 rounded-full ${isOpen ? 'bg-green-500' : 'bg-red-500'}`} />
          {statusText}
        </div>
      </div>

      {/* Today's hours (if available and details requested) */}
      {showDetails && status.todayHours && (
        <span className="text-sm text-gray-600">
          Today: {status.todayHours}
        </span>
      )}
    </div>
  );
}

// Hook for checking if ordering is allowed
export function useCanOrder() {
  const { selectedBranch } = useBusiness();
  const { orderType, deliveryTiming } = useCart();

  if (!selectedBranch) {
    return { canOrder: false, reason: "No branch selected" };
  }

  const serviceType = orderType === "pickup" ? "pickup" : "delivery";
  
  // If ordering for later, always allow (assuming business has timing data)
  if (deliveryTiming === "later") {
    return { canOrder: true, reason: "Scheduled order" };
  }

  // For immediate orders, check if business is currently open
  const isOpen = isBranchOpen(selectedBranch, serviceType);
  
  if (!isOpen) {
    const status = getBranchStatus(selectedBranch, serviceType);
    return { 
      canOrder: false, 
      reason: `${serviceType === "pickup" ? "Pickup" : "Delivery"} is currently closed${status.todayHours ? `. Today's hours: ${status.todayHours}` : ""}` 
    };
  }

  return { canOrder: true, reason: "Open for orders" };
}
