"use client";

import { useBusiness } from "@/context/business-context";
import { useCart } from "@/context/cart-context";

type BusinessHoursStatusProps = {
  className?: string;
  showDetails?: boolean;
};

export default function BusinessHoursStatus({
  className = "",
  showDetails = false,
}: BusinessHoursStatusProps) {
  const { selectedBranch, isBranchOpen, getBranchStatus } = useBusiness();

  if (!selectedBranch) {
    return null;
  }

  // Get current status
  const isOpen = isBranchOpen();
  const status = getBranchStatus();

  // Status styling
  const statusClasses = isOpen
    ? "text-green-600 bg-green-50 border-green-200"
    : "text-red-600 bg-red-50 border-red-200";

  const statusText =
    status.status === "open_24_7"
      ? "Open 24/7"
      : isOpen
      ? "Open Now"
      : "Closed";

  return (
    <div className={`inline-flex items-center gap-2 ${className}`}>
      {/* Status indicator */}
      <div
        className={`px-2 py-1 rounded-full text-xs font-medium border ${statusClasses}`}
      >
        <div className="flex items-center gap-1">
          <div
            className={`w-2 h-2 rounded-full ${
              isOpen ? "bg-green-500" : "bg-red-500"
            }`}
          />
          {statusText}
        </div>
      </div>

      {/* Today's hours (if available and details requested) */}
      {showDetails && status.todayHours && (
        <span className="text-sm text-gray-600">
          Today: {status.todayHours}
        </span>
      )}
    </div>
  );
}

// Hook for checking if ordering is allowed
export function useCanOrder() {
  const { selectedBranch, isBranchOpen, getBranchStatus } = useBusiness();
  const { deliveryTiming } = useCart();

  if (!selectedBranch) {
    return { canOrder: false, reason: "No branch selected" };
  }

  // If ordering for later, always allow (assuming business has timing data)
  if (deliveryTiming === "later") {
    return { canOrder: true, reason: "Scheduled order" };
  }

  // For immediate orders, check if business is currently open
  const isOpen = isBranchOpen();

  if (!isOpen) {
    const status = getBranchStatus();
    return {
      canOrder: false,
      reason: `Business is currently closed${
        status.todayHours ? `. Today's hours: ${status.todayHours}` : ""
      }`,
    };
  }

  return { canOrder: true, reason: "Open for orders" };
}
