"use client";

import { useState, useRef, useEffect } from "react";
import ClockIcon from "./icons/clock-icon";
import ScheduleIcon from "./icons/schedule-icon";
import SubscriptionIcon from "./icons/subscription-icon";

const STORAGE_KEY = "ezeats-delivery-timing";

type DeliveryTimingSelectProps = {
  value: string;
  onChange: (value: string) => void;
  orderType: "delivery" | "pickup";
  className?: string;
};

export default function DeliveryTimingSelect({
  value: propValue,
  onChange,
  orderType,
  className = "",
}: DeliveryTimingSelectProps) {
  // Initialize state with localStorage value or prop value
  const [value, setValue] = useState(() => {
    // Check if we're in the browser
    if (typeof window !== "undefined") {
      const savedValue = localStorage.getItem(STORAGE_KEY);
      return savedValue || propValue;
    }
    return propValue;
  });

  const [isOpen, setIsOpen] = useState(false);
  const selectRef = useRef<HTMLDivElement>(null);

  // Sync with prop value when it changes
  useEffect(() => {
    if (propValue !== value) {
      setValue(propValue);
    }
  }, [propValue]);

  // Options with icons
  const options = [
    {
      value: "now",
      label: orderType === "delivery" ? "Deliver now" : "Pickup now",
      icon: <ClockIcon size={18} className="mr-2" />,
    },
    {
      value: "later",
      label: "Schedule for later",
      icon: <ScheduleIcon size={18} className="mr-2" />,
    },
    {
      value: "weekly",
      label: "Weekly subscription",
      icon: <SubscriptionIcon size={18} className="mr-2" />,
    },
    {
      value: "biweekly",
      label: "Bi-weekly subscription",
      icon: <SubscriptionIcon size={18} className="mr-2" />,
    },
  ];

  // Get the selected option
  const selectedOption =
    options.find((option) => option.value === value) || options[0];

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        selectRef.current &&
        !selectRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Notify parent on initial load if we loaded from localStorage
  useEffect(() => {
    if (typeof window !== "undefined") {
      const savedValue = localStorage.getItem(STORAGE_KEY);
      if (savedValue && savedValue !== propValue) {
        onChange(savedValue);
      }
    }
  }, []);

  // Function to handle option selection
  const handleOptionSelect = (optionValue: string) => {
    // Only update if value is changing
    if (optionValue !== value) {
      // Save to localStorage
      localStorage.setItem(STORAGE_KEY, optionValue);

      // Update internal state
      setValue(optionValue);

      // Dispatch a custom event to notify that the user changed the delivery timing
      const event = new CustomEvent("userChangedDeliveryTiming");
      document.dispatchEvent(event);

      // Call the onChange handler
      onChange(optionValue);
    }

    setIsOpen(false);
  };

  return (
    <div className={`relative ${className}`} ref={selectRef}>
      <div className="relative">
        <button
          type="button"
          className="w-full h-[62px] py-2 pr-2 pl-[13px] border border-gray-300 rounded-md bg-white text-black font-poppins text-left flex items-center"
          onClick={() => setIsOpen(!isOpen)}
        >
          <div className="flex items-center">
            {selectedOption.icon}
            {selectedOption.label}
          </div>
          <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
            <svg
              className={`h-5 w-5 text-gray-400 transition-transform ${
                isOpen ? "transform rotate-180" : ""
              }`}
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
          </div>
        </button>
      </div>

      {isOpen && (
        <div className="absolute z-10 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg">
          {options.map((option) => (
            <button
              key={option.value}
              type="button"
              className={`w-full text-left px-4 py-2 flex items-center hover:bg-gray-100 ${
                option.value === value ? "bg-gray-50" : ""
              }`}
              onClick={() => handleOptionSelect(option.value)}
            >
              {option.icon}
              {option.label}
            </button>
          ))}
        </div>
      )}
    </div>
  );
}
