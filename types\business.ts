export interface BusinessSettings {
  cod: number
  coupon: {
    override: boolean
  }
  editor: {
    enabled: boolean
  }
  product: {
    show_out_of_stock_price: boolean
  }
  checkout: {
    button_text: string
    force_user_auth: boolean
  }
  gratuity: {
    type: number
    label: string
    value: number
    pickup: boolean
    enabled: boolean
    delivery: boolean
  }
  future_order: number
}

export interface PaymentOption {
  name: string
  enabled: boolean
  method: string
  account_id: string
  account_details: {
    charges: number
    charges_type: string
    preauth: boolean
    preauth_charges: number
  }
}

export interface BranchSettings {
  cart: {
    tax: string
    discount: string
    tax_type: string
    minimum_spent: string
    online_payment_tax: string
  }
}

export interface Branch {
  id: number
  r_id: number
  address: string
  location: string
  country: string
  city: string
  phoneno: string
  lat: string
  lng: string
  status: string
  email: string
  delivery: number
  pickup: number
  reservation: number
  delivery_service: number
  time_zone: string
  settings: string
  delivery_settings: string
  email_settings: string | null
  sms_settings: string
  timing: string
  parsedSettings?: BranchSettings
}

export interface BusinessLocationsResponse {
  status: number
  message: string
  result: {
    name: string
    logo: string
    currencycode: string
    aws_flag: number
    time_zone: string
    username: string
    discount: number
    tax: number
    minimum_spend: number
    inventory: number
    delivery_charges_tax: number
    tax_type: number
    settings: BusinessSettings
    contact_phone: string
    email: string
    contact_name: string
    tax_before_discount: number
    preparation_time: number
    decimal_places: number
    payment_options: PaymentOption[]
    address_api: string
    branches: Branch[]
  }
}
