import type {
  BusinessLocationsResponse,
  Branch,
  BranchSettings,
  BranchTiming,
} from "@/types/business";
import { BUSINESS_ID, BRANCH_ID } from "@/constants/app-constants";

const BUSINESS_LOCATIONS_API = `https://d9gwfwdle3.execute-api.us-east-1.amazonaws.com/prod/v1/business/${BUSINESS_ID}/locations/${BRANCH_ID}`;

// Parse the branch settings from JSON string to object
function parseBranchSettings(branch: Branch): Branch {
  try {
    if (branch.settings) {
      const parsedSettings = JSON.parse(branch.settings) as BranchSettings;
      return {
        ...branch,
        parsedSettings,
      };
    }
  } catch (error) {
    console.error("Error parsing branch settings");
  }
  return branch;
}

// Parse the branch timing from JSON string to object
function parseBranchTiming(branch: Branch): Branch {
  console.log("🔧 parseBranchTiming - Input branch:", {
    id: branch.id,
    timing: branch.timing,
  });
  try {
    if (branch.timing) {
      const parsedTiming = JSON.parse(branch.timing) as BranchTiming;
      console.log(
        "✅ parseBranchTiming - Successfully parsed timing:",
        parsedTiming
      );
      return {
        ...branch,
        parsedTiming,
      };
    } else {
      console.log("⚠️ parseBranchTiming - No timing data found for branch");
    }
  } catch (error) {
    console.error("❌ Error parsing branch timing:", error);
  }
  return branch;
}

export async function fetchBusinessLocations(): Promise<BusinessLocationsResponse> {
  try {
    const response = await fetch(BUSINESS_LOCATIONS_API);

    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }

    const data = (await response.json()) as BusinessLocationsResponse;

    // Parse branch settings and timing for each branch
    if (data.result && data.result.branches) {
      data.result.branches = data.result.branches
        .map(parseBranchSettings)
        .map(parseBranchTiming);
    }

    return data;
  } catch (error) {
    console.error("Error fetching business locations");
    throw error;
  }
}

// Get minimum spend amount from branch settings
export function getMinimumSpend(branch: Branch): number {
  if (branch.parsedSettings?.cart?.minimum_spent) {
    return Number.parseFloat(branch.parsedSettings.cart.minimum_spent);
  }
  return 0;
}

// Get formatted address for a branch
export function getFormattedBranchAddress(branch: Branch): string {
  return `${branch.address}, ${branch.city}, ${branch.location}, ${branch.country}`;
}

// Convert minutes since midnight to time string (e.g., 600 -> "10:00")
function minutesToTimeString(minutes: number): string {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  return `${hours.toString().padStart(2, "0")}:${mins
    .toString()
    .padStart(2, "0")}`;
}

// Get current day number (1-7, Monday-Sunday)
function getCurrentDayNumber(): number {
  const today = new Date();
  const dayOfWeek = today.getDay(); // 0 = Sunday, 1 = Monday, etc.
  return dayOfWeek === 0 ? 7 : dayOfWeek; // Convert Sunday (0) to 7
}

// Get current time in minutes since midnight
function getCurrentTimeInMinutes(): number {
  const now = new Date();
  return now.getHours() * 60 + now.getMinutes();
}

// Check if business is open based on hours
export function isBranchOpen(branch: Branch): boolean {
  console.log("🕐 isBranchOpen - Starting check for branch:", branch.id);

  if (!branch.parsedTiming) {
    console.log("⚠️ isBranchOpen - No parsed timing data, assuming open");
    return true; // If no timing data, assume open
  }

  const currentDay = getCurrentDayNumber().toString();
  const currentTime = getCurrentTimeInMinutes();

  console.log(
    "🕐 isBranchOpen - Current day:",
    currentDay,
    "Current time (minutes):",
    currentTime
  );
  console.log("🕐 isBranchOpen - Available timing data:", branch.parsedTiming);

  // Get timing from hours key
  const dayTimings = branch.parsedTiming.hours?.[currentDay];
  console.log(
    "🕐 isBranchOpen - Day timings for day",
    currentDay,
    ":",
    dayTimings
  );

  if (!dayTimings || dayTimings.length === 0) {
    console.log(
      "❌ isBranchOpen - No timing data for this day, returning closed"
    );
    return false; // No timing data for this day means closed
  }

  // Check each timing slot for the day
  for (const timingSlot of dayTimings) {
    console.log("🔍 isBranchOpen - Checking timing slot:", timingSlot);

    if (!timingSlot.timing || timingSlot.timing.length === 0) {
      console.log(
        "✅ isBranchOpen - Empty timing array found, business is 24/7"
      );
      return true; // Empty timing array means 24/7
    }

    // Check each time range in the timing slot
    for (const timeRange of timingSlot.timing) {
      console.log("🔍 isBranchOpen - Checking time range:", timeRange);

      if (timeRange.length === 2) {
        // Only use the first element (minutes format) for comparison
        const openTime = typeof timeRange[0] === "number" ? timeRange[0] : 0;
        const closeTime = typeof timeRange[1] === "number" ? timeRange[1] : 0;

        console.log(
          "🔍 isBranchOpen - Parsed times - Open:",
          openTime,
          "Close:",
          closeTime
        );

        // If open and close times are the same, business is closed
        if (openTime === closeTime) {
          console.log(
            "⚠️ isBranchOpen - Same open/close times, skipping this range"
          );
          continue;
        }

        // Check if current time falls within this range
        if (currentTime >= openTime && currentTime <= closeTime) {
          console.log(
            "✅ isBranchOpen - Current time is within range, business is OPEN"
          );
          return true;
        } else {
          console.log("❌ isBranchOpen - Current time is outside range");
        }
      }
    }
  }

  console.log(
    "❌ isBranchOpen - No valid time ranges found, business is CLOSED"
  );
  return false;
}

// Get business status with next opening time
export function getBranchStatus(branch: Branch): {
  isOpen: boolean;
  status: "open" | "closed" | "open_24_7";
  nextOpenTime?: string;
  todayHours?: string;
} {
  console.log(
    "📊 getBranchStatus - Starting status check for branch:",
    branch.id
  );

  if (!branch.parsedTiming) {
    console.log("⚠️ getBranchStatus - No parsed timing data, returning open");
    return { isOpen: true, status: "open" };
  }

  const currentDay = getCurrentDayNumber().toString();
  const dayTimings = branch.parsedTiming.hours?.[currentDay];

  console.log(
    "📊 getBranchStatus - Current day:",
    currentDay,
    "Day timings:",
    dayTimings
  );

  if (!dayTimings || dayTimings.length === 0) {
    console.log(
      "❌ getBranchStatus - No timing data for this day, returning closed"
    );
    return { isOpen: false, status: "closed" };
  }

  // Check if any timing slot is 24/7
  for (const timingSlot of dayTimings) {
    if (!timingSlot.timing || timingSlot.timing.length === 0) {
      console.log(
        "✅ getBranchStatus - Found 24/7 timing slot, returning open_24_7"
      );
      return { isOpen: true, status: "open_24_7" };
    }
  }

  const isOpen = isBranchOpen(branch);
  console.log("📊 getBranchStatus - isBranchOpen result:", isOpen);

  // Get today's hours for display
  let todayHours = "";
  if (dayTimings.length > 0 && dayTimings[0].timing.length > 0) {
    const timeRanges = dayTimings[0].timing;
    console.log(
      "📊 getBranchStatus - Processing time ranges for display:",
      timeRanges
    );

    const ranges = timeRanges
      .filter(
        (range) =>
          range.length === 2 &&
          typeof range[0] === "number" &&
          typeof range[1] === "number" &&
          range[0] !== range[1]
      )
      .map(
        (range) =>
          `${minutesToTimeString(range[0] as number)} - ${minutesToTimeString(
            range[1] as number
          )}`
      );
    todayHours = ranges.join(", ");
    console.log("📊 getBranchStatus - Formatted today's hours:", todayHours);
  }

  const result = {
    isOpen,
    status: (isOpen ? "open" : "closed") as "open" | "closed",
    todayHours: todayHours || undefined,
  };

  console.log("📊 getBranchStatus - Final result:", result);
  return result;
}

// Check if branch allows ordering for future delivery
export function canOrderForLater(branch: Branch): boolean {
  // This function can be extended to check business settings for future orders
  // For now, return true if branch has timing data
  return !!branch.parsedTiming;
}
