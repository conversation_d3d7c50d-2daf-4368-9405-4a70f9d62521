import type { BusinessLocationsResponse, Branch, BranchSettings } from "@/types/business"
import { BUSINESS_ID, BRANCH_ID } from "@/constants/app-constants"

const BUSINESS_LOCATIONS_API = `https://d9gwfwdle3.execute-api.us-east-1.amazonaws.com/prod/v1/business/${BUSINESS_ID}/locations/${BRANCH_ID}`

// Parse the branch settings from JSON string to object
function parseBranchSettings(branch: Branch): Branch {
  try {
    if (branch.settings) {
      const parsedSettings = JSON.parse(branch.settings) as BranchSettings
      return {
        ...branch,
        parsedSettings,
      }
    }
  } catch (error) {
    console.error("Error parsing branch settings")
  }
  return branch
}

export async function fetchBusinessLocations(): Promise<BusinessLocationsResponse> {
  try {
    const response = await fetch(BUSINESS_LOCATIONS_API)

    if (!response.ok) {
      throw new Error(`API error: ${response.status}`)
    }

    const data = (await response.json()) as BusinessLocationsResponse

    // Parse branch settings for each branch
    if (data.result && data.result.branches) {
      data.result.branches = data.result.branches.map(parseBranchSettings)
    }

    return data
  } catch (error) {
    console.error("Error fetching business locations")
    throw error
  }
}

// Get minimum spend amount from branch settings
export function getMinimumSpend(branch: Branch): number {
  if (branch.parsedSettings?.cart?.minimum_spent) {
    return Number.parseFloat(branch.parsedSettings.cart.minimum_spent)
  }
  return 0
}

// Get formatted address for a branch
export function getFormattedBranchAddress(branch: Branch): string {
  return `${branch.address}, ${branch.city}, ${branch.location}, ${branch.country}`
}
