"use client"

import { createContext, useContext, useState, useEffect, type ReactNode } from "react"
import { fetchBusinessLocations } from "@/services/business-api"
import type { BusinessLocationsResponse, Branch } from "@/types/business"
import { BusinessSkeleton } from "@/components/business-skeleton"

type BusinessContextType = {
  businessData: BusinessLocationsResponse | null
  isLoading: boolean
  error: string | null
  selectedBranch: Branch | null
  setSelectedBranch: (branch: Branch) => void
  googleMapsApiKey: string | null
  minimumSpend: number
  businessLogo: string | null
  businessName: string
}

const BusinessContext = createContext<BusinessContextType | undefined>(undefined)

export function BusinessProvider({ children }: { children: ReactNode }) {
  const [businessData, setBusinessData] = useState<BusinessLocationsResponse | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedBranch, setSelectedBranch] = useState<Branch | null>(null)
  const [googleMapsApiKey, setGoogleMapsApiKey] = useState<string | null>(null)
  const [minimumSpend, setMinimumSpend] = useState(0)
  const [businessLogo, setBusinessLogo] = useState<string | null>(null)
  const [businessName, setBusinessName] = useState("EZeats")

  useEffect(() => {
    async function loadBusinessData() {
      try {
        setIsLoading(true)
        const data = await fetchBusinessLocations()
        setBusinessData(data)

        // Set Google Maps API key
        if (data.result && data.result.address_api) {
          setGoogleMapsApiKey(data.result.address_api)
        }

        // Set business logo
        if (data.result && data.result.logo) {
          setBusinessLogo(data.result.logo)
        }

        // Set business name
        if (data.result && data.result.name) {
          setBusinessName(data.result.name)
        }

        // Set default selected branch
        if (data.result && data.result.branches && data.result.branches.length > 0) {
          const defaultBranch = data.result.branches[0]
          setSelectedBranch(defaultBranch)

          // Set minimum spend from branch settings
          if (defaultBranch.parsedSettings?.cart?.minimum_spent) {
            setMinimumSpend(Number.parseFloat(defaultBranch.parsedSettings.cart.minimum_spent))
          }
        }
      } catch (err) {
        console.error("Failed to load business data:", err)
        setError("Failed to load business information. Please try again later.")
      } finally {
        setIsLoading(false)
      }
    }

    loadBusinessData()
  }, [])

  // Update minimum spend when selected branch changes
  useEffect(() => {
    if (selectedBranch?.parsedSettings?.cart?.minimum_spent) {
      setMinimumSpend(Number.parseFloat(selectedBranch.parsedSettings.cart.minimum_spent))
    } else {
      setMinimumSpend(0)
    }
  }, [selectedBranch])

  // If loading, show skeleton
  if (isLoading) {
    return <BusinessSkeleton />
  }

  return (
    <BusinessContext.Provider
      value={{
        businessData,
        isLoading,
        error,
        selectedBranch,
        setSelectedBranch,
        googleMapsApiKey,
        minimumSpend,
        businessLogo,
        businessName,
      }}
    >
      {children}
    </BusinessContext.Provider>
  )
}

export function useBusiness() {
  const context = useContext(BusinessContext)
  if (context === undefined) {
    throw new Error("useBusiness must be used within a BusinessProvider")
  }
  return context
}
