"use client";

import { useBusiness } from "@/context/business-context";
import { isBranchOpen, getBranchStatus } from "@/services/business-api";

export default function TimingDebug() {
  const { selectedBranch, isBranchOpen: contextIsBranchOpen, getBranchStatus: contextGetBranchStatus } = useBusiness();

  const handleTestTiming = () => {
    console.log("🧪 TIMING DEBUG TEST STARTED");
    console.log("🧪 Selected branch:", selectedBranch);
    
    if (!selectedBranch) {
      console.log("❌ No selected branch found");
      return;
    }

    console.log("🧪 Branch timing data:", {
      id: selectedBranch.id,
      rawTiming: selectedBranch.timing,
      parsedTiming: selectedBranch.parsedTiming,
      hasParsedTiming: !!selectedBranch.parsedTiming
    });

    // Test direct function calls
    console.log("🧪 Testing direct function calls:");
    const directIsOpen = isBranchOpen(selectedBranch);
    const directStatus = getBranchStatus(selectedBranch);
    console.log("🧪 Direct isBranchOpen result:", directIsOpen);
    console.log("🧪 Direct getBranchStatus result:", directStatus);

    // Test context function calls
    console.log("🧪 Testing context function calls:");
    const contextIsOpen = contextIsBranchOpen();
    const contextStatus = contextGetBranchStatus();
    console.log("🧪 Context isBranchOpen result:", contextIsOpen);
    console.log("🧪 Context getBranchStatus result:", contextStatus);

    console.log("🧪 TIMING DEBUG TEST COMPLETED");
  };

  const handleShowCurrentTime = () => {
    const now = new Date();
    const dayOfWeek = now.getDay(); // 0 = Sunday, 1 = Monday, etc.
    const currentDay = dayOfWeek === 0 ? 7 : dayOfWeek; // Convert Sunday (0) to 7
    const currentTime = now.getHours() * 60 + now.getMinutes();
    
    console.log("🕐 Current Time Info:");
    console.log("🕐 Date:", now.toString());
    console.log("🕐 Day of week (JS):", dayOfWeek);
    console.log("🕐 Day number (our format):", currentDay);
    console.log("🕐 Time in minutes:", currentTime);
    console.log("🕐 Time formatted:", `${Math.floor(currentTime / 60).toString().padStart(2, '0')}:${(currentTime % 60).toString().padStart(2, '0')}`);
  };

  const handleShowBranchTiming = () => {
    if (!selectedBranch?.parsedTiming?.hours) {
      console.log("❌ No timing data available");
      return;
    }

    console.log("📅 Branch Timing Data:");
    const hours = selectedBranch.parsedTiming.hours;
    
    for (let day = 1; day <= 7; day++) {
      const dayNames = ['', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
      const dayTimings = hours[day.toString()];
      
      console.log(`📅 ${dayNames[day]} (Day ${day}):`, dayTimings);
      
      if (!dayTimings || dayTimings.length === 0) {
        console.log(`   No timing data - CLOSED`);
      } else {
        for (const slot of dayTimings) {
          if (!slot.timing || slot.timing.length === 0) {
            console.log(`   24/7 - OPEN`);
          } else {
            console.log(`   Timing slots:`, slot.timing);
          }
        }
      }
    }
  };

  if (!selectedBranch) {
    return (
      <div className="p-4 bg-yellow-100 border border-yellow-400 rounded">
        <p>⚠️ No branch selected. Timing debug not available.</p>
      </div>
    );
  }

  return (
    <div className="p-4 bg-blue-100 border border-blue-400 rounded mb-4">
      <h3 className="font-bold mb-2">🧪 Timing Debug Panel</h3>
      <p className="text-sm mb-3">Selected Branch: {selectedBranch.id}</p>
      
      <div className="space-x-2">
        <button 
          onClick={handleTestTiming}
          className="px-3 py-1 bg-blue-500 text-white rounded text-sm"
        >
          Test Timing Functions
        </button>
        
        <button 
          onClick={handleShowCurrentTime}
          className="px-3 py-1 bg-green-500 text-white rounded text-sm"
        >
          Show Current Time
        </button>
        
        <button 
          onClick={handleShowBranchTiming}
          className="px-3 py-1 bg-purple-500 text-white rounded text-sm"
        >
          Show Branch Hours
        </button>
      </div>
      
      <p className="text-xs mt-2 text-gray-600">
        Check browser console for detailed logs
      </p>
    </div>
  );
}
